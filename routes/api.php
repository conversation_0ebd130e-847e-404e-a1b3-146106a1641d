<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\PhoneController;
use App\Http\Controllers\Api\UserController;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Support\Facades\RateLimiter;

// Define rate limiters
RateLimiter::for('api', function (Request $request) {
    return Limit::perMinute(60)->by($request->ip());
});

RateLimiter::for('phone-api', function (Request $request) {
    return Limit::perMinute(5)->by($request->ip());
});

RateLimiter::for('email-api', function (Request $request) {
    return Limit::perMinute(5)->by($request->ip());
});

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Public API routes with rate limiting
Route::middleware('throttle:phone-api')->group(function () {
    Route::post('/phone', [PhoneController::class, 'store']);
});

Route::middleware('throttle:email-api')->group(function () {
    Route::post('/user/email', [UserController::class, 'storeEmail']);
});
