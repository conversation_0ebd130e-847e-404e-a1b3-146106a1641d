<?php

namespace App\Livewire;

use App\Models\Phone as PhoneNumber;
use App\Models\User;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Components\TextInput;
use Livewire\Component;
use Illuminate\Contracts\View\View;

class Phone extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public $showConfirmMessage = false;

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make("phone_number")
                    ->label("Telephone")
                    ->tel()
                    ->required()
                    ->maxlength(20)
                    ->autofocus()
                    ->placeholder("0655529888")
                    ->suffixIcon("heroicon-o-phone")
                    ->unique('phones', 'phone_number')
                    ->regex('/^[0-9+\s()-]{5,20}$/'),
            ])
            ->statePath("data")
            ->model(PhoneNumber::class);
    }

    public function create(): void
    {
        $data = $this->form->getState();

        try {
            // Store phone number without creating a user
            // Users should be created through proper registration
            $record = PhoneNumber::create([
                'phone_number' => $data['phone_number']
            ]);

            $this->showConfirmMessage = true;
            $this->form->fill([
                "phone_number" => "",
            ]);
        } catch (\Exception $e) {
            // Add error handling
            session()->flash('error', 'Une erreur est survenue. Veuillez réessayer.');
        }
    }

    public function hideConfirmMessage()
    {
        $this->showConfirmMessage = false;
    }

    public function render(): View
    {
        return view("livewire.phone");
    }
}
