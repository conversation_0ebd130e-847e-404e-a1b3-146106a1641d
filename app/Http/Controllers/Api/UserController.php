<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreUserEmailRequest;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class UserController extends Controller
{
    /**
     * Store an email address in the users table.
     *
     * @param StoreUserEmailRequest $request
     * @return JsonResponse
     */
    public function storeEmail(StoreUserEmailRequest $request): JsonResponse
    {
        try {
            // Request is already validated by StoreUserEmailRequest
            $validated = $request->validated();

            // Create the user record with just the email
            // Generate a random password and remember token for security
            $user = User::create([
                'email' => $validated['email'],
                'password' => bcrypt(Str::random(16)), // Random secure password
                'remember_token' => Str::random(10),
            ]);

            // Log the successful creation
            Log::info('User email stored successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Email stored successfully',
                'data' => [
                    'id' => $user->id,
                    'email' => $user->email,
                ]
            ], 201);
        } catch (\Exception $e) {
            // Log the error
            Log::error('Failed to store user email', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'email' => $request->email ?? null,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to store email',
                'error' => config('app.debug') ? $e->getMessage() : 'An unexpected error occurred'
            ], 500);
        }
    }
}
