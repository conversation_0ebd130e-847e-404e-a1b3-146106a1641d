<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StorePhoneRequest;
use App\Models\Phone;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class PhoneController extends Controller
{
    /**
     * Store a phone number in the database.
     *
     * @param StorePhoneRequest $request
     * @return JsonResponse
     */
    public function store(StorePhoneRequest $request): JsonResponse
    {
        try {
            // Request is already validated by StorePhoneRequest
            $validated = $request->validated();

            // Check if an email was provided to associate with a user
            $userId = null;
            if ($request->has('email')) {
                $user = User::where('email', $request->email)->first();
                if ($user) {
                    $userId = $user->id;
                }
            }

            // Create the phone record
            $phone = Phone::create([
                'phone_number' => $validated['phone_number'],
                'user_id' => $userId,
            ]);

            // Log the successful creation
            Log::info('Phone number stored successfully', [
                'phone_id' => $phone->id,
                'phone_number' => $phone->phone_number,
                'user_id' => $userId,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Phone number stored successfully',
                'data' => $phone
            ], 201);
        } catch (\Exception $e) {
            // Log the error
            Log::error('Failed to store phone number', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'phone_number' => $request->phone_number ?? null,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to store phone number',
                'error' => config('app.debug') ? $e->getMessage() : 'An unexpected error occurred'
            ], 500);
        }
    }
}
