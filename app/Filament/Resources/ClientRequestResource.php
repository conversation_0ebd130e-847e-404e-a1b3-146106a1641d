<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ClientRequestResource\Pages;
use App\Filament\Resources\ClientRequestResource\RelationManagers;
use App\Models\ClientRequest;
use App\Models\User; // Import User model
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea; // Already used indirectly, but good to have if schema gets complex
use Filament\Forms\Components\Select; // Already used, but good for clarity
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ClientRequestResource extends Resource
{
    protected static ?string $model = ClientRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text'; // Example icon

    protected static ?string $navigationGroup = 'Client Management'; // Example group

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->label('Client')
                    ->options(User::where('role', 'client')->whereNotNull('name')->pluck('name', 'id')) // Fetch users with 'client' role
                    ->searchable()
                    ->required(),
                Forms\Components\Textarea::make('problem_description')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\Select::make('status')
                    ->options([
                        'new' => 'New',
                        'pending_review' => 'Pending Review',
                        'action_required' => 'Action Required',
                        'closed' => 'Closed',
                    ])
                    ->default('new')
                    ->required(),

                Repeater::make('plans')
                    ->relationship()
                    ->schema([
                        FileUpload::make('file_path')
                            ->label('Plan File')
                            ->required()
                            ->disk('public')
                            ->directory('client_plans')
                            ->storeFileNamesIn('original_file_name')
                            ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                            ->maxSize(10240), // 10MB
                        // original_file_name is handled by storeFileNamesIn and should not be a user input here
                        // It will be saved to the Plan model automatically.
                        Select::make('file_type')
                            ->options([
                                'pdf' => 'PDF',
                                'jpg' => 'JPEG Image',
                                'png' => 'PNG Image',
                            ])
                            ->required(),
                    ])
                    ->columnSpanFull()
                    ->addActionLabel('Add Plan')
                    ->collapsible()
                    ->collapsed(true), // Start collapsed if preferred

                Repeater::make('requestPhotos')
                    ->relationship() // Correct relationship name from ClientRequest model
                    ->schema([
                        FileUpload::make('file_path')
                            ->label('Photo')
                            ->required()
                            ->disk('public')
                            ->directory('client_request_photos')
                            ->storeFileNamesIn('original_file_name')
                            ->image()
                            ->imageEditor()
                            ->maxSize(5120), // 5MB
                        // original_file_name is handled by storeFileNamesIn
                        Textarea::make('caption')
                            ->label('Caption')
                            ->columnSpanFull(),
                    ])
                    ->columnSpanFull()
                    ->addActionLabel('Add Photo')
                    ->collapsible()
                    ->collapsed(true), // Start collapsed if preferred
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('client.name') // Assuming 'client' relationship on ClientRequest model
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('problem_description')
                    ->limit(50)
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\PlansRelationManager::class,
            RelationManagers\RequestPhotosRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClientRequests::route('/'),
            'create' => Pages\CreateClientRequest::route('/create'),
            'edit' => Pages\EditClientRequest::route('/{record}/edit'),
            'view' => Pages\ViewClientRequest::route('/{record}'),
        ];
    }
}