<?php

namespace App\Filament\Resources\ClientRequestResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Storage; // For file uploads

class RequestPhotosRelationManager extends RelationManager
{
    protected static string $relationship = 'requestPhotos'; // Matches the relationship name in ClientRequest model

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\FileUpload::make('file_path')
                    ->label('Photo')
                    ->required()
                    ->disk('public') // Or your preferred disk
                    ->directory('client_request_photos') // Directory within the disk
                    ->storeFileNamesIn('original_file_name')
                    ->image() // Specify it's an image for previews etc.
                    ->imageEditor() // Optional: adds an image editor
                    ->maxSize(5120), // 5MB max
                Forms\Components\TextInput::make('original_file_name')
                    ->label('Original File Name')
                    ->disabled()
                    ->dehydrated(false),
                Forms\Components\Textarea::make('caption')
                    ->label('Caption')
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('original_file_name')
            ->columns([
                Tables\Columns\ImageColumn::make('file_path') // Display image directly
                    ->disk('public')
                    ->label('Photo'),
                Tables\Columns\TextColumn::make('original_file_name')
                    ->label('File Name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('caption')
                    ->limit(50),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('download')
                        ->label('Download')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->url(fn ($record) => Storage::disk('public')->url($record->file_path))
                        ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
