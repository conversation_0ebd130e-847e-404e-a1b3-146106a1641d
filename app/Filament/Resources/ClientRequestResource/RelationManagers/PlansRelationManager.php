<?php

namespace App\Filament\Resources\ClientRequestResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Storage; // For file uploads

class PlansRelationManager extends RelationManager
{
    protected static string $relationship = 'plans';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\FileUpload::make('file_path')
                    ->label('Plan File')
                    ->required()
                    ->disk('public') // Or your preferred disk
                    ->directory('client_plans') // Directory within the disk
                    ->storeFileNamesIn('original_file_name') // Stores original name in this column
                    ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                    ->maxSize(10240), // 10MB max
                Forms\Components\TextInput::make('original_file_name')
                    ->label('Original File Name')
                    ->disabled() // Or hidden, as it's set by FileUpload
                    ->dehydrated(false), // Don't save it directly if storeFileNamesIn is used
                Forms\Components\Select::make('file_type')
                    ->options([
                        'pdf' => 'PDF',
                        'jpg' => 'JPEG Image',
                        'png' => 'PNG Image',
                        // Add other relevant types
                    ])
                    ->required(), // Consider making this automatically derived from file upload
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('original_file_name')
            ->columns([
                Tables\Columns\TextColumn::make('original_file_name')
                    ->label('File Name'),
                Tables\Columns\TextColumn::make('file_type')
                    ->badge(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
                // Add a download action or link if needed
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('download')
                    ->label('Download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->url(fn ($record) => Storage::disk('public')->url($record->file_path))
                    ->openUrlInNewTab(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
