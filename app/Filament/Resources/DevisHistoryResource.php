<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DevisHistoryResource\Pages;
use App\Models\DevisHistory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class DevisHistoryResource extends Resource
{
    protected static ?string $model = DevisHistory::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?string $navigationGroup = 'Business Management';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('devis_id')
                    ->relationship('devis', 'number')
                    ->required(),
                Forms\Components\TextInput::make('version_number')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('number')
                    ->required()
                    ->numeric(),
                Forms\Components\DatePicker::make('start_works_date'),
                Forms\Components\DatePicker::make('end_works_date'),
                Forms\Components\Select::make('status')
                    ->options([
                        'estimation' => 'Estimation',
                        'devis' => 'Devis',
                        'signé' => 'Signé',
                        'refusé' => 'Refusé',
                    ])
                    ->default('estimation'),
                Forms\Components\Select::make('dossier_id')
                    ->relationship('dossier', 'name')
                    ->required(),
                Forms\Components\Select::make('cctp_id')
                    ->relationship('cctp', 'path')
                    ->nullable(),
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload()
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('devis.number')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('version_number')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('number')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_works_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_works_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->searchable(),
                Tables\Columns\TextColumn::make('dossier.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('cctp.path')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDevisHistory::route('/'),
            'create' => Pages\CreateDevisHistory::route('/create'),
            'edit' => Pages\EditDevisHistory::route('/{record}/edit'),
        ];
    }
}
