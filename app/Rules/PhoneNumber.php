<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class PhoneNumber implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Standard phone number validation regex
        // Allows digits, +, spaces, parentheses, and hyphens
        // Length between 5 and 20 characters
        if (!preg_match('/^[0-9+\s()-]{5,20}$/', $value)) {
            $fail('The :attribute must be a valid phone number format.');
        }
    }
}
