<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DevisHistory extends Model
{
    /**
    * Le nom de la table associée au modèle.
    *
    * @var string
    */
   protected $table = 'devis_histories';

   /**
    * Les attributs qui peuvent être assignés en masse.
    *
    * @var array
    */
   protected $fillable = [
       'devis_id',
       'version_number',
       'number',
       'start_works_date',
       'end_works_date',
       'status',
       'dossier_id',
       'cctp_id',
       'user_id',
   ];

   /**
    * Get the user that owns the devis history.
    */
   public function user(): BelongsTo
   {
       return $this->belongsTo(User::class);
   }

   /**
    * Get the devis that owns the devis history.
    */
   public function devis(): BelongsTo
   {
       return $this->belongsTo(Devis::class);
   }

   /**
    * Get the dossier that owns the devis history.
    */
   public function dossier(): BelongsTo
   {
       return $this->belongsTo(Dossier::class);
   }

   /**
    * Get the cctp that owns the devis history.
    */
   public function cctp(): BelongsTo
   {
       return $this->belongsTo(Cctp::class);
   }
}
