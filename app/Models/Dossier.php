<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes; // Add this line

class Dossier extends Model
{
    /** @use HasFactory<\Database\Factories\DossierFactory> */
    use HasFactory, SoftDeletes; // Add SoftDeletes here

    protected $guarded = [];

    public function notes()
    {
        return $this->hasMany(Note::class);
    }

    public function photos()
    {
        return $this->hasMany(Photo::class);
    }

    public function audios() {
        return $this->hasMany(Audio::class);
    }

    public function devis() {
        return $this->hasOne(Devis::class);
    }
}
