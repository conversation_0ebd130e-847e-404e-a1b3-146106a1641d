<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Signature extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'devis_id',
        'signature_path',
        'signed_at',
    ];

    /**
     * Get the devis that owns the signature.
     */
    public function devis(): BelongsTo
    {
        return $this->belongsTo(Devis::class);
    }
}
