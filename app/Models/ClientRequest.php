<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ClientRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'problem_description',
        'status',
    ];

    /**
     * Get the client (user) that owns the request.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the plans for the client request.
     */
    public function plans(): HasMany
    {
        return $this->hasMany(Plan::class);
    }

    /**
     * Get the photos for the client request.
     */
    public function requestPhotos(): Has<PERSON><PERSON>
    {
        return $this->hasMany(RequestPhoto::class);
    }
}
