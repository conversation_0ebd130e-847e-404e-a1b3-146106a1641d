<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Devis extends Model
{
    use HasFactory;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'number',
        'start_works_date',
        'end_works_date',
        'status',
        'version',
        'dossier_id',
        'cctp_id'
    ];

    /**
     * Get the dossier that owns the devis.
     */
    public function dossier(): BelongsTo
    {
        return $this->belongsTo(Dossier::class);
    }

    /**
     * Get the cctp that owns the devis.
     */
    public function cctp(): BelongsTo
    {
        return $this->belongsTo(Cctp::class);
    }

    /**
     * Get the histories for the devis.
     */
    public function histories(): HasMany
    {
        return $this->hasMany(DevisHistory::class);
    }

    /**
     * Get the signature for the devis.
     */
    public function signature(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Signature::class);
    }
}
