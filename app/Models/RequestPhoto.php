<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RequestPhoto extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_request_id',
        'file_path',
        'original_file_name',
        'caption',
    ];

    /**
     * Get the client request that owns the photo.
     */
    public function clientRequest(): BelongsTo
    {
        return $this->belongsTo(ClientRequest::class);
    }
}
