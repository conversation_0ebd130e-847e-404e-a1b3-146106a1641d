<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("phones", function (Blueprint $table) {
            $table->id();
            $table->string("phone_number", 20)->index();
            $table->foreignId("user_id")->nullable()->constrained();
            $table->foreignId("company_id")->nullable()->constrained();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists("phones");
    }
};
