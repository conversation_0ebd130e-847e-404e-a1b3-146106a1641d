<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('photos', function (Blueprint $table) {
            $table->text('annotation')->nullable();
            $table->enum('type', ['photo', 'audio', 'cctp', 'note'])->default('photo');
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::table('photos', function (Blueprint $table) {
            $table->dropColumn('annotation');
            $table->dropColumn('type');
            $table->dropSoftDeletes();
        });
    }
};
