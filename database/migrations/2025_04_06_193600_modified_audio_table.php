<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('audio', function (Blueprint $table) {
            $table->text('transcription')->nullable();
            $table->enum('type', ['photo', 'audio', 'cctp', 'note'])->default('audio');
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::table('audio', function (Blueprint $table) {
            $table->dropColumn('transcription');
            $table->dropColumn('type');
            $table->dropSoftDeletes();
        });
    }
};
