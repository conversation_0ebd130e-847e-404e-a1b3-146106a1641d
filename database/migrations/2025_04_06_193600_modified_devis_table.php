<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('devis', function (Blueprint $table) {
            $table->enum('status', ['estimation', 'devis', 'signé', 'refusé'])->default('estimation');
            $table->integer('version')->default(1);
            $table->foreignId('cctp_id')->nullable()->constrained();
        });
    }

    public function down(): void
    {
        Schema::table('devis', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropColumn('version');
            $table->dropForeign(['cctp_id']);
            $table->dropColumn('cctp_id');
        });
    }
};
