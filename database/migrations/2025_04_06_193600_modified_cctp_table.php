<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('cctps', function (Blueprint $table) {
            $table->enum('type', ['photo', 'audio', 'cctp', 'note'])->default('cctp');
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::table('cctps', function (Blueprint $table) {
            $table->dropColumn('type');
            $table->dropSoftDeletes();
        });
    }
};
