<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('devis_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('devis_id')->constrained();
            $table->integer('version_number');
            $table->integer("number");
            $table->dateTime("start_works_date")->nullable();
            $table->dateTime("end_works_date")->nullable();
            $table->enum('status', ['estimation', 'devis', 'signé', 'refusé'])->default('estimation');
            $table->foreignId("dossier_id")->constrained();
            $table->foreignId('cctp_id')->nullable()->constrained();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('devis_histories');
    }
};
