<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, drop foreign key constraints from tables we're keeping that reference tables we're removing

        // Drop foreign key constraints from devis table that reference cctps and dossiers
        if (Schema::hasTable('devis')) {
            Schema::table('devis', function (Blueprint $table) {
                if (Schema::hasColumn('devis', 'cctp_id')) {
                    $table->dropForeign(['cctp_id']);
                }
                if (Schema::hasColumn('devis', 'dossier_id')) {
                    $table->dropForeign(['dossier_id']);
                }
            });
        }

        // Drop foreign key constraints from companies table that reference dossiers
        if (Schema::hasTable('companies')) {
            Schema::table('companies', function (Blueprint $table) {
                if (Schema::hasColumn('companies', 'dossier_id')) {
                    $table->dropForeign(['dossier_id']);
                }
            });
        }

        // Drop foreign key constraints from photos table that reference dossiers
        if (Schema::hasTable('photos')) {
            Schema::table('photos', function (Blueprint $table) {
                if (Schema::hasColumn('photos', 'dossier_id')) {
                    $table->dropForeign(['dossier_id']);
                }
            });
        }

        // Now drop the tables in the correct order (children first, then parents)

        // Drop tables that reference other tables we're removing
        Schema::dropIfExists('signatures'); // references devis (but we're keeping devis)
        Schema::dropIfExists('devis_histories'); // references devis, dossiers, cctps, users
        Schema::dropIfExists('audio'); // references dossiers

        // Drop tables that are referenced by others
        Schema::dropIfExists('phones'); // referenced by users and companies (but we're keeping those)
        Schema::dropIfExists('cctps'); // referenced by devis and devis_histories
        Schema::dropIfExists('dossiers'); // referenced by many tables

        // Drop history table if it exists (no migration found for it)
        Schema::dropIfExists('history');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Note: This is a destructive migration.
        // Reversing it would require recreating all the tables and their data.
        // For safety, we're leaving this empty as the data cannot be recovered.
        // If you need to reverse this migration, you would need to:
        // 1. Recreate all the dropped tables with their original structure
        // 2. Restore the foreign key constraints
        // 3. Restore any data from backups

        throw new \Exception('This migration cannot be reversed as it involves dropping tables and data. Please restore from backup if needed.');
    }
};
