<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists("audio");
        Schema::dropIfExists("cctps");
        Schema::dropIfExists("dossier");
        Schema::dropIfExists("signature");
        Schema::dropIfExists("devis_history");
        Schema::dropIfExists("phone");
        Schema::dropIfExists("history"); 
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    
    }
};
