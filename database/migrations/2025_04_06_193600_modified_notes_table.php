<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('notes', function (Blueprint $table) {
            $table->string('image_path')->nullable();
            $table->enum('type', ['photo', 'audio', 'cctp', 'note'])->default('note');
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::table('notes', function (Blueprint $table) {
            $table->dropColumn('image_path');
            $table->dropColumn('type');
            $table->dropSoftDeletes();
        });
    }
};
