<?php

namespace Tests\Feature\Api;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PhoneApiTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_store_a_phone_number()
    {
        $response = $this->postJson('/api/phone', [
            'phone_number' => '0655529888',
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Phone number stored successfully',
            ]);

        $this->assertDatabaseHas('phones', [
            'phone_number' => '0655529888',
        ]);
    }

    /** @test */
    public function it_validates_phone_number_format()
    {
        $response = $this->postJson('/api/phone', [
            'phone_number' => 'invalid-phone',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
            ]);

        $this->assertDatabaseMissing('phones', [
            'phone_number' => 'invalid-phone',
        ]);
    }

    /** @test */
    public function it_prevents_duplicate_phone_numbers()
    {
        // First request should succeed
        $this->postJson('/api/phone', [
            'phone_number' => '0655529888',
        ])->assertStatus(201);

        // Second request with the same phone number should fail
        $response = $this->postJson('/api/phone', [
            'phone_number' => '0655529888',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
            ]);
    }

    /** @test */
    public function it_can_associate_phone_with_existing_user()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Submit phone with email
        $response = $this->postJson('/api/phone', [
            'phone_number' => '0655529888',
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('phones', [
            'phone_number' => '0655529888',
            'user_id' => $user->id,
        ]);
    }

    /** @test */
    public function it_validates_email_exists_when_provided()
    {
        $response = $this->postJson('/api/phone', [
            'phone_number' => '0655529888',
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
            ]);
    }
}
