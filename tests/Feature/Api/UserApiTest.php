<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserApiTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_store_an_email()
    {
        $response = $this->postJson('/api/user/email', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Email stored successfully',
            ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function it_validates_email_format()
    {
        $response = $this->postJson('/api/user/email', [
            'email' => 'invalid-email',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
            ]);

        $this->assertDatabaseMissing('users', [
            'email' => 'invalid-email',
        ]);
    }

    /** @test */
    public function it_prevents_duplicate_emails()
    {
        // First request should succeed
        $this->postJson('/api/user/email', [
            'email' => '<EMAIL>',
        ])->assertStatus(201);

        // Second request with the same email should fail
        $response = $this->postJson('/api/user/email', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
            ]);
    }
}
