<div>
    <form wire:submit="create">
        {{ $this->form }}

        @if($showConfirmMessage)
        <div wire:transition class="relative inset-x-0 bottom-0 bg-green-500 text-white px-4 py-2 text-left mt-2 rounded-lg">
            <button wire:click.prevent="hideConfirmMessage" class="absolute top-0 right-0 mt-2 mr-2 text-gray-600 hover:text-gray-800 focus:outline-none">
                 <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                 </svg>
             </button>
            Envoi bien effectué.<br/>
            Vous serez contacté dans le plus bref delai.
        </div>
        @endif
        <button type="submit" class="mt-5 px-6 py-1 bg-blue-500 text-white font-semibold rounded-lg shadow-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 transition duration-300">
            Envoyer
        </button>
    </form>

    <x-filament-actions::modals />
</div>
